import apiClient from './client';

export interface School {
  id: number;
  name: string;
  description: string;
  location: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SchoolCreate {
  name: string;
  description: string;
  location: string;
  is_active?: boolean;
}

export interface SchoolUpdate {
  name?: string;
  description?: string;
  location?: string;
  is_active?: boolean;
}

export const getSchools = async (): Promise<School[]> => {
  try {
    console.log('Fetching schools from public API...');
    // Use public endpoint first since this is for profile completion
    const response = await apiClient.get<School[]>('/schools/public');

    if (response.data && response.data.length > 0) {
      console.log('Schools fetched successfully from public API:', response.data);
      return response.data;
    }

    console.log('Public API returned empty schools list, trying authenticated endpoint...');
    throw new Error('Empty schools list from public API');
  } catch (error) {
    console.error('Error fetching schools from public API:', error);

    // Try the authenticated endpoint as fallback
    try {
      console.log('Trying authenticated schools endpoint...');
      const response = await apiClient.get<School[]>('/schools');
      if (response.data && response.data.length > 0) {
        console.log('Schools fetched successfully from authenticated API:', response.data);
        return response.data;
      }
      throw new Error('Empty schools list from authenticated API');
    } catch (authError) {
      console.error('Authenticated endpoint also failed:', authError);

      // As a last resort, use external API or hardcoded list
      console.log('Using external API or hardcoded list as final fallback');
      return await fetchNigerianUniversities();
    }
  }
};

// Comprehensive list of Nigerian universities
const nigerianUniversities: School[] = [
  { id: 1, name: 'University of Lagos', description: 'UNILAG', location: 'Lagos', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 2, name: 'University of Ibadan', description: 'UI', location: 'Ibadan', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 3, name: 'Obafemi Awolowo University', description: 'OAU', location: 'Ile-Ife', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 4, name: 'University of Nigeria, Nsukka', description: 'UNN', location: 'Nsukka', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 5, name: 'Ahmadu Bello University', description: 'ABU', location: 'Zaria', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 6, name: 'University of Benin', description: 'UNIBEN', location: 'Benin City', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 7, name: 'University of Port Harcourt', description: 'UNIPORT', location: 'Port Harcourt', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 8, name: 'University of Ilorin', description: 'UNILORIN', location: 'Ilorin', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 9, name: 'University of Jos', description: 'UNIJOS', location: 'Jos', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 10, name: 'University of Calabar', description: 'UNICAL', location: 'Calabar', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 11, name: 'University of Maiduguri', description: 'UNIMAID', location: 'Maiduguri', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 12, name: 'Bayero University Kano', description: 'BUK', location: 'Kano', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 13, name: 'Nnamdi Azikiwe University', description: 'UNIZIK', location: 'Awka', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 14, name: 'Federal University of Technology, Akure', description: 'FUTA', location: 'Akure', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 15, name: 'Federal University of Technology, Minna', description: 'FUTMINNA', location: 'Minna', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 16, name: 'Federal University of Technology, Owerri', description: 'FUTO', location: 'Owerri', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 17, name: 'Abubakar Tafawa Balewa University', description: 'ATBU', location: 'Bauchi', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 18, name: 'Federal University, Oye-Ekiti', description: 'FUOYE', location: 'Oye-Ekiti', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 19, name: 'Federal University, Lokoja', description: 'FULOKOJA', location: 'Lokoja', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  { id: 20, name: 'Lagos State University', description: 'LASU', location: 'Lagos', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
];

/**
 * Fetches Nigerian universities from an external API
 * This is a fallback in case the internal API fails
 */
const fetchNigerianUniversities = async (): Promise<School[]> => {
  try {
    console.log('Fetching Nigerian universities from external source...');
    const axios = (await import('axios')).default;

    // Try to fetch from an external API that provides Nigerian universities
    // If this fails, we'll use our hardcoded list
    try {
      // This is a placeholder URL - replace with a real API if available
      const response = await axios.get('https://universities.hipolabs.com/search?country=nigeria', {
        timeout: 5000 // 5 second timeout
      });

      if (response.data && Array.isArray(response.data) && response.data.length > 0) {
        console.log('Successfully fetched universities from external API');

        // Map the external API response to our School interface
        return response.data.map((uni: any, index: number) => ({
          id: index + 1,
          name: uni.name,
          description: uni.domains?.[0] || 'N/A',
          location: uni.state_province || 'Nigeria',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }));
      }

      throw new Error('No universities found in external API response');
    } catch (externalApiError) {
      console.error('Error fetching from external API:', externalApiError);
      console.log('Using hardcoded list of Nigerian universities');
      return nigerianUniversities;
    }
  } catch (error) {
    console.error('Error in fetchNigerianUniversities:', error);
    return nigerianUniversities;
  }
};

export const getSchoolsPublic = async (): Promise<School[]> => {
  try {
    console.log('Fetching public schools from internal API...');
    const response = await apiClient.get<School[]>('/schools/public');

    if (response.data && response.data.length > 0) {
      console.log('Public schools fetched successfully from internal API:', response.data);
      return response.data;
    }

    console.log('Internal API returned empty schools list, trying fallback...');
    throw new Error('Empty schools list from internal API');
  } catch (error) {
    console.error('Error fetching public schools from internal API:', error);

    // Try a fallback approach with direct axios import
    try {
      console.log('Trying fallback approach with direct API call...');
      const axios = (await import('axios')).default;
      const fallbackResponse = await axios.get<School[]>('http://localhost:8001/api/v1/schools/public', {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        timeout: 5000 // 5 second timeout
      });

      if (fallbackResponse.data && fallbackResponse.data.length > 0) {
        console.log('Fallback approach successful:', fallbackResponse.data);
        return fallbackResponse.data;
      }

      throw new Error('Empty schools list from fallback API call');
    } catch (fallbackError) {
      console.error('Fallback approach also failed:', fallbackError);

      // As a last resort, fetch from external API or use hardcoded list
      console.log('Using external API or hardcoded list as final fallback');
      return await fetchNigerianUniversities();
    }
  }
};

export const getSchool = async (id: number): Promise<School> => {
  const response = await apiClient.get<School>(`/schools/${id}`);
  return response.data;
};

export const createSchool = async (data: SchoolCreate): Promise<School> => {
  const response = await apiClient.post<School>('/schools', data);
  return response.data;
};

export const updateSchool = async (id: number, data: SchoolUpdate): Promise<School> => {
  const response = await apiClient.put<School>(`/schools/${id}`, data);
  return response.data;
};

export const deleteSchool = async (id: number): Promise<School> => {
  const response = await apiClient.delete<School>(`/schools/${id}`);
  return response.data;
};
