import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Container,
  Grid,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  Autocomplete,
  ListSubheader,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { completeProfile } from '../../api/auth';
import { useAuth } from '../../contexts/AuthContext';
import { getSchools } from '../../api/schools';
import { getDepartmentsBySchool, Department } from '../../api/departments';
import { useQuery } from '@tanstack/react-query';

// Define steps for the profile completion process
const steps = ['Personal Info', 'Education', 'Location', 'Confirmation'];

// Nigerian states
const nigerianStates = [
  '<PERSON>bia', '<PERSON><PERSON>', '<PERSON>kwa Ibo<PERSON>', 'Anamb<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Benue', 'Borno',
  'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe', 'Imo',
  'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara', 'Lagos', 'Nasarawa',
  'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau', 'Rivers', 'Sokoto', 'Taraba',
  'Yobe', 'Zamfara'
];

// Comprehensive list of academic departments
const commonDepartments = [
  // Arts & Humanities
  'African Studies', 'American Studies', 'Anthropology', 'Archaeology', 'Architecture', 'Art History',
  'Arts Management', 'Asian Studies', 'Classical Studies', 'Communication Studies', 'Comparative Literature',
  'Creative Writing', 'Criminal Justice', 'Dance', 'Design', 'Digital Media', 'Drama', 'English Language',
  'English Literature', 'Environmental Studies', 'Ethnic Studies', 'European Studies', 'Film Studies',
  'Fine Arts', 'Foreign Languages', 'French', 'Gender Studies', 'Geography', 'German', 'Graphic Design',
  'History', 'Human Rights', 'Humanities', 'International Relations', 'Italian', 'Japanese', 'Jewish Studies',
  'Journalism', 'Latin American Studies', 'Law', 'Linguistics', 'Literature', 'Mass Communication', 'Media Studies',
  'Middle Eastern Studies', 'Music', 'Philosophy', 'Photography', 'Political Science', 'Psychology',
  'Public Administration', 'Public Policy', 'Public Relations', 'Religious Studies', 'Russian', 'Sociology',
  'Spanish', 'Theatre Arts', 'Urban Planning', 'Visual Arts', 'Women\'s Studies',

  // Business
  'Accounting', 'Actuarial Science', 'Advertising', 'Banking and Finance', 'Business Administration',
  'Business Analytics', 'Business Economics', 'Business Information Systems', 'Business Law', 'Business Management',
  'Commerce', 'Corporate Finance', 'E-Business', 'Economics', 'Entrepreneurship', 'Finance', 'Financial Management',
  'Hospitality Management', 'Human Resource Management', 'Industrial Relations', 'Information Systems',
  'International Business', 'Investment Management', 'Logistics', 'Management', 'Marketing', 'Operations Management',
  'Organizational Behavior', 'Project Management', 'Real Estate', 'Risk Management', 'Sales', 'Supply Chain Management',
  'Taxation', 'Tourism Management',

  // Science & Technology
  'Aerospace Engineering', 'Agricultural Science', 'Applied Mathematics', 'Applied Physics', 'Artificial Intelligence',
  'Astronomy', 'Astrophysics', 'Biochemistry', 'Bioinformatics', 'Biology', 'Biomedical Engineering', 'Biotechnology',
  'Botany', 'Chemical Engineering', 'Chemistry', 'Civil Engineering', 'Computer Engineering', 'Computer Science',
  'Cybersecurity', 'Data Science', 'Electrical Engineering', 'Electronics', 'Environmental Engineering',
  'Environmental Science', 'Food Science', 'Forensic Science', 'Genetics', 'Geology', 'Geophysics',
  'Industrial Engineering', 'Information Technology', 'Marine Biology', 'Materials Science', 'Mathematics',
  'Mechanical Engineering', 'Mechatronics', 'Meteorology', 'Microbiology', 'Mining Engineering', 'Molecular Biology',
  'Nanotechnology', 'Neuroscience', 'Nuclear Engineering', 'Oceanography', 'Petroleum Engineering', 'Pharmaceutical Sciences',
  'Physics', 'Robotics', 'Software Engineering', 'Statistics', 'Systems Engineering', 'Telecommunications', 'Zoology',

  // Health & Medicine
  'Anatomy', 'Anesthesiology', 'Audiology', 'Cardiology', 'Dentistry', 'Dermatology', 'Dietetics', 'Emergency Medicine',
  'Endocrinology', 'Epidemiology', 'Exercise Science', 'Gastroenterology', 'Genetics', 'Geriatrics', 'Gynecology',
  'Health Administration', 'Health Informatics', 'Health Sciences', 'Hematology', 'Immunology', 'Kinesiology',
  'Medical Laboratory Science', 'Medicine and Surgery', 'Nephrology', 'Neurology', 'Nursing', 'Nutrition',
  'Obstetrics', 'Occupational Therapy', 'Oncology', 'Ophthalmology', 'Optometry', 'Orthopedics', 'Pathology',
  'Pediatrics', 'Pharmacy', 'Physical Therapy', 'Physician Assistant Studies', 'Physiology', 'Psychiatry',
  'Public Health', 'Radiology', 'Rehabilitation', 'Respiratory Therapy', 'Speech Therapy', 'Sports Medicine',
  'Surgery', 'Toxicology', 'Urology', 'Veterinary Medicine'
];

// Interface for Institution
interface Institution {
  id: number;
  name: string;
  country?: string;
  web_page?: string;
}

// Interface for University API response
interface UniversityApiResponse {
  name: string;
  country: string;
  web_pages: string[];
  domains: string[];
  alpha_two_code: string;
}

// Validation schemas for each step
const validationSchemas = [
  // Step 1: Personal Info
  Yup.object({
    other_name: Yup.string().optional(),
    gender: Yup.string().required('Gender is required'),
    phone_number: Yup.string()
      .required('Phone number is required')
      .matches(/^[0-9]{10,15}$/, 'Phone number must be between 10 and 15 digits'),
  }),

  // Step 2: Education
  Yup.object({
    department: Yup.string().required('Department is required'),
    department_id: Yup.number().nullable(),
    level: Yup.string().required('Level is required'),
    institution: Yup.string().required('Institution is required'), // Making institution required
    institution_id: Yup.number().required('Institution is required'),
  }),

  // Step 3: Location
  Yup.object({
    date_of_birth: Yup.date()
      .required('Date of birth is required')
      .max(new Date(), 'Date of birth cannot be in the future')
      .test(
        'is-adult',
        'You must be at least 16 years old',
        function(value) {
          if (!value) return false;
          const today = new Date();
          const birthDate = new Date(value);
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }
          return age >= 16;
        }
      ),
    state_of_origin: Yup.string().required('State of origin is required'),
  }),

  // Step 4: Confirmation
  Yup.object({}),
];

const SimpleProfileCompletion: React.FC = () => {
  const navigate = useNavigate();
  const { refreshUser, user } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [profileComplete, setProfileComplete] = useState(false);
  const [selectedSchoolId, setSelectedSchoolId] = useState<number | null>(null);

  // Temporary hardcoded data while backend is being fixed
  const hardcodedSchools = [
    { id: 1, name: 'University of Lagos', description: 'Premier university in Lagos State, Nigeria', location: 'Lagos, Nigeria', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 2, name: 'University of Ibadan', description: 'First university in Nigeria, located in Ibadan', location: 'Ibadan, Oyo State, Nigeria', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 3, name: 'Ahmadu Bello University', description: 'Leading university in Northern Nigeria', location: 'Zaria, Kaduna State, Nigeria', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 4, name: 'University of Nigeria, Nsukka', description: 'First indigenous university in Nigeria', location: 'Nsukka, Enugu State', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 5, name: 'Obafemi Awolowo University', description: 'Federal university in Osun State', location: 'Ile-Ife, Osun State', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 6, name: 'University of Benin', description: 'Federal university in Edo State', location: 'Benin City, Edo State', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 7, name: 'University of Port Harcourt', description: 'Federal university in Rivers State', location: 'Port Harcourt, Rivers State', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    { id: 8, name: 'University of Ilorin', description: 'Federal university in Kwara State', location: 'Ilorin, Kwara State', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
  ];

  // Hardcoded departments mapping
  const hardcodedDepartments = {
    1: [ // University of Lagos
      { id: 1, name: 'Computer Science', description: 'Department of Computer Science and Information Technology', school_id: 1, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 2, name: 'Electrical Engineering', description: 'Department of Electrical and Electronics Engineering', school_id: 1, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 3, name: 'Medicine', description: 'College of Medicine', school_id: 1, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 4, name: 'Law', description: 'Faculty of Law', school_id: 1, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 5, name: 'Business Administration', description: 'Lagos Business School', school_id: 1, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    ],
    2: [ // University of Ibadan
      { id: 6, name: 'Mathematics', description: 'Department of Mathematics', school_id: 2, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 7, name: 'Physics', description: 'Department of Physics', school_id: 2, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 8, name: 'Chemistry', description: 'Department of Chemistry', school_id: 2, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 9, name: 'English', description: 'Department of English', school_id: 2, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    ],
    3: [ // Ahmadu Bello University
      { id: 10, name: 'Agricultural Engineering', description: 'Department of Agricultural and Bioresources Engineering', school_id: 3, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 11, name: 'Economics', description: 'Department of Economics', school_id: 3, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
      { id: 12, name: 'Political Science', description: 'Department of Political Science', school_id: 3, is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
    ],
  };

  // Use hardcoded data for now (will be replaced with API calls when backend is fixed)
  const [schools, setSchools] = useState(hardcodedSchools);
  const [isLoadingSchools, setIsLoadingSchools] = useState(false);
  const [schoolsError, setSchoolsError] = useState(null);

  // Try to fetch from API but fall back to hardcoded data
  useEffect(() => {
    const fetchSchools = async () => {
      setIsLoadingSchools(true);
      try {
        const apiSchools = await getSchools();
        if (apiSchools && apiSchools.length > 0) {
          console.log('Successfully loaded schools from API:', apiSchools);
          setSchools(apiSchools);
          setSchoolsError(null);
        } else {
          console.log('API returned empty schools, using hardcoded data');
        }
      } catch (error) {
        console.error('Failed to fetch schools from API, using hardcoded data:', error);
        setSchoolsError(error);
      } finally {
        setIsLoadingSchools(false);
      }
    };

    fetchSchools();
  }, []);

  // Fetch departments for selected school
  const {
    data: departments = [],
    isLoading: isLoadingDepartments,
    error: departmentsError
  } = useQuery({
    queryKey: ['departments', selectedSchoolId],
    queryFn: () => selectedSchoolId ? getDepartmentsBySchool(selectedSchoolId) : Promise.resolve([]),
    enabled: !!selectedSchoolId,
    retry: 3,
  });

  // For backward compatibility, keep the institutions list
  const institutions: Institution[] = [
    { id: 1, name: 'University of Lagos' },
    { id: 2, name: 'University of Ibadan' },
    { id: 3, name: 'Obafemi Awolowo University' },
    { id: 4, name: 'University of Nigeria, Nsukka' },
    { id: 5, name: 'Ahmadu Bello University' },
    { id: 6, name: 'University of Benin' },
    { id: 7, name: 'University of Port Harcourt' },
    { id: 8, name: 'University of Ilorin' },
    { id: 9, name: 'University of Jos' },
    { id: 10, name: 'University of Calabar' },
    { id: 11, name: 'University of Maiduguri' },
    { id: 12, name: 'Bayero University Kano' },
    { id: 13, name: 'Nnamdi Azikiwe University' },
    { id: 14, name: 'Federal University of Technology, Akure' },
    { id: 15, name: 'Federal University of Technology, Minna' },
    { id: 16, name: 'Federal University of Technology, Owerri' },
    { id: 17, name: 'Abubakar Tafawa Balewa University' },
    { id: 18, name: 'Federal University, Oye-Ekiti' },
    { id: 19, name: 'Federal University, Lokoja' },
    { id: 20, name: 'Lagos State University' },
    { id: 21, name: 'University of Uyo' },
    { id: 22, name: 'Federal University of Agriculture, Abeokuta' },
    { id: 23, name: 'Federal University of Agriculture, Makurdi' },
    { id: 24, name: 'University of Abuja' },
    { id: 25, name: 'Ladoke Akintola University of Technology' },
    { id: 26, name: 'Adekunle Ajasin University' },
    { id: 27, name: 'Ekiti State University' },
    { id: 28, name: 'Olabisi Onabanjo University' },
    { id: 29, name: 'Osun State University' },
    { id: 30, name: 'Tai Solarin University of Education' },
    { id: 31, name: 'Covenant University' },
    { id: 32, name: 'Babcock University' },
    { id: 33, name: 'Redeemer\'s University' },
    { id: 34, name: 'Landmark University' },
    { id: 35, name: 'Bowen University' },
    { id: 36, name: 'Afe Babalola University' },
    { id: 37, name: 'American University of Nigeria' },
    { id: 38, name: 'Benson Idahosa University' },
    { id: 39, name: 'Igbinedion University' },
    { id: 40, name: 'Pan-Atlantic University' }
  ];

  // No loading state needed since we're using hardcoded data
  const loadingInstitutions = false;

  // State for search terms
  const [institutionSearchTerm, setInstitutionSearchTerm] = useState('');
  const [departmentSearchTerm, setDepartmentSearchTerm] = useState('');

  // Filtered departments based on search term
  const filteredDepartments = departmentSearchTerm
    ? commonDepartments.filter(dept =>
        dept.toLowerCase().includes(departmentSearchTerm.toLowerCase()))
    : commonDepartments;

  // Log the institutions to verify they're available
  useEffect(() => {
    console.log('Schools from API:', schools);
    console.log('Schools error:', schoolsError);
    console.log('Total schools:', schools.length);
    console.log('Is loading schools:', isLoadingSchools);
    console.log('Selected school ID:', selectedSchoolId);
    console.log('Departments:', departments);
    console.log('Departments error:', departmentsError);
  }, [schools, schoolsError, isLoadingSchools, selectedSchoolId, departments, departmentsError]);



  // Create formik instance with dynamic validation schema
  const formik = useFormik({
    initialValues: {
      other_name: '',
      gender: 'male',
      phone_number: '',
      department: '',
      department_id: null as number | null,
      level: '100',
      institution: '',
      institution_id: null as number | null,
      date_of_birth: new Date(2000, 0, 1).toISOString().split('T')[0],
      state_of_origin: '',
    },
    // We'll handle validation manually in the handleNext function
    validationSchema: validationSchemas[activeStep],
    onSubmit: async (values) => {
      // If not on the last step, move to the next step
      if (activeStep < steps.length - 1) {
        handleNext();
        return;
      }

      // On the last step, submit the form
      setLoading(true);
      setError(null);

      try {
        console.log('Submitting profile data:', values);

        // Prepare data for API
        const profileData = {
          ...values,
          institution_id: values.institution_id || undefined,
          department_id: values.department_id || undefined,
        };

        // Call the API to complete profile
        await completeProfile(profileData);

        // Refresh user data
        await refreshUser();

        setProfileComplete(true);

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      } catch (err: any) {
        console.error('Profile completion error:', err);

        // Check if it's an email verification error
        if (err.response?.data?.detail?.includes('Email must be verified') ||
            err.response?.headers?.['x-email-verification-required']) {
          // Redirect to email verification pending page
          navigate('/verify-email-pending', {
            state: {
              email: user?.email,
              message: 'Please verify your email address before completing your profile.'
            }
          });
          return;
        }

        setError(err.response?.data?.detail || 'Failed to complete profile. Please try again.');
      } finally {
        setLoading(false);
      }
    },
  });

  const handleNext = () => {
    // Validate current step before proceeding
    const currentSchema = validationSchemas[activeStep];
    try {
      const validFields = {};
      Object.keys(formik.values).forEach(key => {
        validFields[key] = formik.values[key];
      });

      currentSchema.validateSync(validFields, { abortEarly: false });
      setActiveStep((prevStep) => prevStep + 1);
      setError(null);
    } catch (err) {
      // Trigger validation to show errors
      Object.keys(formik.values).forEach(key => {
        formik.setFieldTouched(key, true);
      });
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Render step content
  const getStepContent = (step: number) => {
    switch (step) {
      case 0: // Personal Info
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="other_name"
                name="other_name"
                label="Other Name (Optional)"
                variant="outlined"
                value={formik.values.other_name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.other_name && Boolean(formik.errors.other_name)}
                helperText={formik.touched.other_name && formik.errors.other_name}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl
                fullWidth
                error={formik.touched.gender && Boolean(formik.errors.gender)}
                required
              >
                <InputLabel id="gender-label" shrink={true}>Gender</InputLabel>
                <Select
                  labelId="gender-label"
                  id="gender"
                  name="gender"
                  value={formik.values.gender}
                  label="Gender *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  displayEmpty
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
                {formik.touched.gender && formik.errors.gender && (
                  <FormHelperText>{formik.errors.gender}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="phone_number"
                name="phone_number"
                label="Phone Number"
                variant="outlined"
                value={formik.values.phone_number}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.phone_number && Boolean(formik.errors.phone_number)}
                helperText={formik.touched.phone_number && formik.errors.phone_number}
                required
                placeholder="e.g., 08012345678"
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
        );

      case 1: // Education
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel id="institution-label" shrink={true}>Institution (Optional)</InputLabel>
                <Select
                  labelId="institution-label"
                  id="institution"
                  name="institution"
                  value={formik.values.institution}
                  label="Institution (Optional)"
                  onChange={(e) => {
                    const selectedSchool = schools.find(s => s.name === e.target.value);
                    formik.setFieldValue('institution_id', selectedSchool ? selectedSchool.id : null);
                    formik.setFieldValue('institution', e.target.value);
                    setSelectedSchoolId(selectedSchool ? selectedSchool.id : null);
                    // Reset department when school changes
                    formik.setFieldValue('department', '');
                    formik.setFieldValue('department_id', null);
                  }}
                  onBlur={formik.handleBlur}
                  displayEmpty
                  MenuProps={{
                    PaperProps: {
                      style: {
                        maxHeight: 300,
                      },
                    },
                  }}
                >
                  <MenuItem value="">
                    <em>Select an institution</em>
                  </MenuItem>

                  {/* Use schools from API */}
                  {isLoadingSchools ? (
                    <MenuItem disabled>
                      <CircularProgress size={20} /> Loading institutions...
                    </MenuItem>
                  ) : schoolsError ? (
                    <MenuItem disabled>
                      Error loading institutions. Please refresh the page.
                    </MenuItem>
                  ) : schools.length === 0 ? (
                    <MenuItem disabled>
                      No institutions available. Please contact support.
                    </MenuItem>
                  ) : (
                    schools.map((school) => (
                      <MenuItem key={school.id} value={school.name}>
                        {school.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
                <FormHelperText>Select your institution (optional)</FormHelperText>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth error={formik.touched.department && Boolean(formik.errors.department)} required>
                <InputLabel id="department-label" shrink={true}>Department</InputLabel>
                <Select
                  labelId="department-label"
                  id="department"
                  name="department"
                  value={formik.values.department}
                  label="Department *"
                  onChange={(e) => {
                    const selectedDept = departments.find(d => d.name === e.target.value);
                    formik.setFieldValue('department_id', selectedDept ? selectedDept.id : null);
                    formik.setFieldValue('department', e.target.value);
                  }}
                  onBlur={formik.handleBlur}
                  displayEmpty
                  disabled={!selectedSchoolId || isLoadingDepartments}
                >
                  <MenuItem value="">
                    <em>Select a department</em>
                  </MenuItem>

                  {!selectedSchoolId ? (
                    <MenuItem disabled>
                      Please select an institution first
                    </MenuItem>
                  ) : isLoadingDepartments ? (
                    <MenuItem disabled>
                      <CircularProgress size={20} /> Loading departments...
                    </MenuItem>
                  ) : departmentsError ? (
                    <MenuItem disabled>
                      Error loading departments. Please try again.
                    </MenuItem>
                  ) : departments.length === 0 ? (
                    <MenuItem disabled>
                      No departments found for selected institution.
                    </MenuItem>
                  ) : (
                    departments.map((dept) => (
                      <MenuItem key={dept.id} value={dept.name}>
                        {dept.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
                {formik.touched.department && formik.errors.department && (
                  <FormHelperText>{formik.errors.department}</FormHelperText>
                )}
                {!formik.touched.department && !formik.errors.department && (
                  <FormHelperText>
                    {!selectedSchoolId
                      ? "Please select an institution first to see available departments."
                      : "Select your department of study."
                    }
                  </FormHelperText>
                )}
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl
                fullWidth
                error={formik.touched.level && Boolean(formik.errors.level)}
                required
              >
                <InputLabel id="level-label" shrink={true}>Level</InputLabel>
                <Select
                  labelId="level-label"
                  id="level"
                  name="level"
                  value={formik.values.level}
                  label="Level *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  displayEmpty
                >
                  <MenuItem value="100">100 Level</MenuItem>
                  <MenuItem value="200">200 Level</MenuItem>
                  <MenuItem value="300">300 Level</MenuItem>
                  <MenuItem value="400">400 Level</MenuItem>
                  <MenuItem value="500">500 Level</MenuItem>
                  <MenuItem value="600">600 Level</MenuItem>
                </Select>
                {formik.touched.level && formik.errors.level && (
                  <FormHelperText>{formik.errors.level}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        );

      case 2: // Location
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="date_of_birth"
                name="date_of_birth"
                label="Date of Birth"
                type="date"
                variant="outlined"
                value={formik.values.date_of_birth}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.date_of_birth && Boolean(formik.errors.date_of_birth)}
                helperText={formik.touched.date_of_birth && formik.errors.date_of_birth}
                InputLabelProps={{
                  shrink: true,
                }}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl
                fullWidth
                error={formik.touched.state_of_origin && Boolean(formik.errors.state_of_origin)}
                required
              >
                <InputLabel id="state-label" shrink={true}>State of Origin</InputLabel>
                <Select
                  labelId="state-label"
                  id="state_of_origin"
                  name="state_of_origin"
                  value={formik.values.state_of_origin}
                  label="State of Origin *"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  displayEmpty
                >
                  {nigerianStates.map((state) => (
                    <MenuItem key={state} value={state}>
                      {state}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.state_of_origin && formik.errors.state_of_origin && (
                  <FormHelperText>{formik.errors.state_of_origin}</FormHelperText>
                )}
              </FormControl>
            </Grid>
          </Grid>
        );

      case 3: // Confirmation
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Confirm Your Information
            </Typography>
            <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.default', borderRadius: 2, mb: 3 }}>
              <Grid container spacing={2}>
                {formik.values.other_name && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Other Name
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {formik.values.other_name}
                    </Typography>
                  </Grid>
                )}
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Gender
                  </Typography>
                  <Typography variant="body1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                    {formik.values.gender}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Phone Number
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.phone_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Institution
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.institution}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Department
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.department}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Level
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.level}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date of Birth
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.date_of_birth}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    State of Origin
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.state_of_origin}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
            <Typography variant="body2" color="text.secondary" paragraph>
              Please review your information above. Click "Complete Profile" to save your profile information.
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ borderRadius: 3, overflow: 'hidden', p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Complete Your Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Please provide the following information to complete your profile
          </Typography>
        </Box>

        {/* Stepper */}
        <Stepper
          activeStep={activeStep}
          alternativeLabel
          sx={{
            mb: 4,
            '& .MuiStepLabel-root .Mui-completed': {
              color: 'success.main',
            },
            '& .MuiStepLabel-root .Mui-active': {
              color: 'primary.main',
            },
          }}
        >
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        {/* Profile completion success message */}
        {profileComplete ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="h5" gutterBottom>
              Profile Completed!
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Your profile has been successfully completed. Redirecting to dashboard...
            </Typography>
            <CircularProgress size={24} sx={{ mt: 2 }} />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            {/* Step content */}
            {getStepContent(activeStep)}

            {/* Navigation buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                variant="outlined"
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
              >
                Back
              </Button>

              <Button
                variant="contained"
                color={activeStep === steps.length - 1 ? 'success' : 'primary'}
                type={activeStep === steps.length - 1 ? 'submit' : 'button'}
                onClick={activeStep === steps.length - 1 ? undefined : handleNext}
                disabled={loading}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : activeStep === steps.length - 1 ? (
                  'Complete Profile'
                ) : (
                  'Next'
                )}
              </Button>
            </Box>
          </form>
        )}
      </Paper>
    </Container>
  );
};

export default SimpleProfileCompletion;
