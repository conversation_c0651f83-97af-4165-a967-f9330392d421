#!/usr/bin/env python3

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Database URL
DATABASE_URL = "sqlite:///./campuspq.db"

def quick_seed():
    """Quick seed function to add basic data"""
    
    # Create engine and session
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    db = SessionLocal()
    
    try:
        print("Starting quick seed...")
        
        # Check if schools table exists and has data
        result = db.execute(text("SELECT COUNT(*) FROM schools")).fetchone()
        school_count = result[0] if result else 0
        print(f"Current schools count: {school_count}")
        
        if school_count == 0:
            print("Adding schools...")
            
            # Insert schools
            schools_sql = """
            INSERT INTO schools (name, description, location, is_active, created_at, updated_at) VALUES
            ('University of Lagos', 'Premier university in Lagos State, Nigeria', 'Lagos, Nigeria', 1, datetime('now'), datetime('now')),
            ('University of Ibadan', 'First university in Nigeria, located in Ibadan', 'Ibadan, Oyo State, Nigeria', 1, datetime('now'), datetime('now')),
            ('Ahmadu Bello University', 'Leading university in Northern Nigeria', 'Zaria, Kaduna State, Nigeria', 1, datetime('now'), datetime('now')),
            ('University of Nigeria, Nsukka', 'First indigenous university in Nigeria', 'Nsukka, Enugu State', 1, datetime('now'), datetime('now')),
            ('Obafemi Awolowo University', 'Federal university in Osun State', 'Ile-Ife, Osun State', 1, datetime('now'), datetime('now'))
            """
            
            db.execute(text(schools_sql))
            db.commit()
            print("Schools added successfully!")
            
            # Insert departments
            departments_sql = """
            INSERT INTO departments (name, description, school_id, is_active, created_at, updated_at) VALUES
            ('Computer Science', 'Department of Computer Science and Information Technology', 1, 1, datetime('now'), datetime('now')),
            ('Electrical Engineering', 'Department of Electrical and Electronics Engineering', 1, 1, datetime('now'), datetime('now')),
            ('Medicine', 'College of Medicine', 1, 1, datetime('now'), datetime('now')),
            ('Mathematics', 'Department of Mathematics', 2, 1, datetime('now'), datetime('now')),
            ('Physics', 'Department of Physics', 2, 1, datetime('now'), datetime('now')),
            ('Agricultural Engineering', 'Department of Agricultural and Bioresources Engineering', 3, 1, datetime('now'), datetime('now')),
            ('Economics', 'Department of Economics', 3, 1, datetime('now'), datetime('now')),
            ('Engineering', 'Faculty of Engineering', 4, 1, datetime('now'), datetime('now')),
            ('Law', 'Faculty of Law', 5, 1, datetime('now'), datetime('now'))
            """
            
            db.execute(text(departments_sql))
            db.commit()
            print("Departments added successfully!")
        
        # Verify the data
        result = db.execute(text("SELECT COUNT(*) FROM schools")).fetchone()
        school_count = result[0] if result else 0
        
        result = db.execute(text("SELECT COUNT(*) FROM departments")).fetchone()
        dept_count = result[0] if result else 0
        
        print(f"Final counts - Schools: {school_count}, Departments: {dept_count}")
        
        # Show some sample data
        print("\nSample schools:")
        schools = db.execute(text("SELECT id, name FROM schools LIMIT 5")).fetchall()
        for school in schools:
            print(f"  {school[0]}: {school[1]}")
        
        print("Quick seed completed successfully!")
        return True
        
    except Exception as e:
        print(f"Quick seed failed: {e}")
        import traceback
        traceback.print_exc()
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == '__main__':
    success = quick_seed()
    sys.exit(0 if success else 1)
