#!/usr/bin/env python3

import sqlite3
import sys
import os

def seed_database():
    """Seed the database with Nigerian institutions and departments"""
    
    # Get the database path
    db_path = os.path.join(os.path.dirname(__file__), 'campuspq.db')
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("Connected to database successfully")
        
        # Check current schools count
        cursor.execute('SELECT COUNT(*) FROM schools')
        count = cursor.fetchone()[0]
        print(f'Current schools count: {count}')
        
        if count == 0:
            print('No schools found. Adding sample schools...')
            
            # Add sample schools
            schools = [
                ('University of Lagos', 'Premier university in Lagos State, Nigeria', 'Lagos, Nigeria', 1),
                ('University of Ibadan', 'First university in Nigeria, located in Ibadan', 'Ibadan, Oyo State, Nigeria', 1),
                ('Ahmadu Bello University', 'Leading university in Northern Nigeria', 'Zaria, Kaduna State, Nigeria', 1),
                ('University of Nigeria, Nsukka', 'First indigenous university in Nigeria', 'Nsukka, Enugu State', 1),
                ('Obafemi Awolowo University', 'Federal university in Osun State', 'Ile-Ife, Osun State', 1),
            ]
            
            cursor.executemany(
                'INSERT INTO schools (name, description, location, is_active) VALUES (?, ?, ?, ?)',
                schools
            )
            
            print(f'Added {len(schools)} schools')
            
            # Get the school IDs
            cursor.execute('SELECT id, name FROM schools')
            school_map = {name: id for id, name in cursor.fetchall()}
            
            # Add sample departments
            departments = [
                ('Computer Science', 'Department of Computer Science and Information Technology', school_map['University of Lagos'], 1),
                ('Electrical Engineering', 'Department of Electrical and Electronics Engineering', school_map['University of Lagos'], 1),
                ('Medicine', 'College of Medicine', school_map['University of Lagos'], 1),
                ('Mathematics', 'Department of Mathematics', school_map['University of Ibadan'], 1),
                ('Physics', 'Department of Physics', school_map['University of Ibadan'], 1),
                ('Agricultural Engineering', 'Department of Agricultural and Bioresources Engineering', school_map['Ahmadu Bello University'], 1),
                ('Economics', 'Department of Economics', school_map['Ahmadu Bello University'], 1),
                ('Engineering', 'Faculty of Engineering', school_map['University of Nigeria, Nsukka'], 1),
                ('Law', 'Faculty of Law', school_map['Obafemi Awolowo University'], 1),
            ]
            
            cursor.executemany(
                'INSERT INTO departments (name, description, school_id, is_active) VALUES (?, ?, ?, ?)',
                departments
            )
            
            print(f'Added {len(departments)} departments')
            
            conn.commit()
            print('Sample data added successfully!')
        else:
            print('Schools already exist in database')
            cursor.execute('SELECT id, name FROM schools LIMIT 10')
            schools = cursor.fetchall()
            for school in schools:
                print(f'  {school[0]}: {school[1]}')
        
        # Verify the data
        cursor.execute('SELECT COUNT(*) FROM schools')
        school_count = cursor.fetchone()[0]
        cursor.execute('SELECT COUNT(*) FROM departments')
        dept_count = cursor.fetchone()[0]
        
        print(f'\nFinal counts:')
        print(f'Schools: {school_count}')
        print(f'Departments: {dept_count}')
        
        conn.close()
        print('Database operation completed successfully!')
        return True
        
    except Exception as e:
        print(f'Database operation failed: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = seed_database()
    sys.exit(0 if success else 1)
